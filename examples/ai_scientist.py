"""An example of using SwarmX to generate ideas for research experiments.

Modified from https://github.com/SakanaAI/AI-Scientist
"""

import asyncio
import inspect
import sys
import typing
from functools import partial, wraps
from typing import Any

from mcp.client.stdio import StdioServerParameters
from mcp.server import FastMCP
from typer import Typer
from typer.core import TyperCommand
from typer.models import CommandFunctionType, Default

from swarmx import Agent, Edge


class AsyncTyper(Typer):
    @staticmethod
    def maybe_run_async(decorator, f):
        if inspect.iscoroutinefunction(f):

            @wraps(f)
            def runner(*args, **kwargs):
                return asyncio.run(f(*args, **kwargs))

            decorator(runner)
        else:
            decorator(f)
        return f

    def callback(self, *args, **kwargs):
        decorator = super().callback(*args, **kwargs)
        return partial(self.maybe_run_async, decorator)

    def command(
        self,
        name: str | None = None,
        *,
        cls: type[TyperCommand] | None = None,
        context_settings: dict | None = None,
        help: str | None = None,
        epilog: str | None = None,
        short_help: str | None = None,
        options_metavar: str = "[OPTIONS]",
        add_help_option: bool = True,
        no_args_is_help: bool = False,
        hidden: bool = False,
        deprecated: bool = False,
        # Rich settings
        rich_help_panel: str | None = Default(None),
    ) -> typing.Callable[[CommandFunctionType], CommandFunctionType]:
        decorator = super().command(
            name=name,
            cls=cls,
            context_settings=context_settings,
            help=help,
            epilog=epilog,
            short_help=short_help,
            options_metavar=options_metavar,
            add_help_option=add_help_option,
            no_args_is_help=no_args_is_help,
            hidden=hidden,
            deprecated=deprecated,
            rich_help_panel=rich_help_panel,
        )
        return partial(self.maybe_run_async, decorator)


reasoning_models = ["deepseek-r1", "deepseek-reasoner", "claude-3.7-sonnet", "qwq"]
mcp = FastMCP()

idea_generator = Agent(
    name="Idea Generator",
    model="gpt-oss:20b",
    instructions="""{{ task_description }}
<experiment.py>
{{ code }}
</experiment.py>

{% if prev_ideas %}
Here are the ideas that you have already generated:

'''
{% for idea in prev_ideas %}
{prev_ideas_string}
{% endfor %}
'''
{% endif %}

Come up with the next impactful and creative idea for research experiments and directions you can feasibly investigate with the code provided.
Note that you will not have access to any additional resources or datasets.
Make sure any idea is not overfit the specific training dataset or model, and has wider significance.

Respond in the following format:

{% if model not in reasoning_models %}
THOUGHT:
<THOUGHT>

NEW IDEA JSON:
{% endif %}
```json
<JSON>
```

{% if model not in reasoning_models %}
In <THOUGHT>, first briefly discuss your intuitions and motivations for the idea. Detail your high-level plan, necessary design choices and ideal outcomes of the experiments. Justify how the idea is different from the existing ones.
{% endif %}

In <JSON>, provide the new idea in JSON format with the following fields:
- "Name": A shortened descriptor of the idea. Lowercase, no spaces, underscores allowed.
- "Title": A title for the idea, will be used for the report writing.
- "Experiment": An outline of the implementation. E.g. which functions need to be added or modified, how results will be obtained, ...
- "Interestingness": A rating from 1 to 10 (lowest to highest).
- "Feasibility": A rating from 1 to 10 (lowest to highest).
- "Novelty": A rating from 1 to 10 (lowest to highest).

Be cautious and realistic on your ratings.
This JSON will be automatically parsed, so ensure the format is precise.
You will have {{ num_reflections }} rounds to iterate on the idea, but do not need to use them all.
""",
)


@mcp.tool(structured_output=True)
def need_reflections(context: dict[str, Any]) -> bool:
    return context.get("num_reflections", 0) > 0


@mcp.tool(structured_output=True)
def no_more_reflections(context: dict[str, Any]) -> bool:
    return context.get("current_round", 0) >= context.get("num_reflections", 0)


idea_refiner = Agent(
    name="Idea Refiner",
    model="gpt-oss:20b",
    instructions="""Round {{ current_round }}/{{ num_reflections }}.
In your thoughts, first carefully consider the quality, novelty, and feasibility of the idea you just created.
Include any other factors that you think are important in evaluating the idea.
Ensure the idea is clear and concise, and the JSON is the correct format.
Do not make things overly complicated.
In the next attempt, try and refine and improve your idea.
Stick to the spirit of the original idea unless there are glaring issues.

Respond in the same format as before:

{% if model not in reasoning_models %}
THOUGHT:
<THOUGHT>

NEW IDEA JSON:
{% endif %}
```json
<JSON>
```

If there is nothing to improve, simply repeat the previous JSON EXACTLY {% if model not in reasoning_models %}after the thought {% endif %}and include "I am done" at the end of the thoughts but before the JSON.
ONLY INCLUDE "I am done" IF YOU ARE MAKING NO MORE CHANGES.""",
)

idea = Agent(
    name="Idea",
    model="gpt-oss:20b",
    nodes={
        "generator": idea_generator,
        "refiner": idea_refiner,
    },
    entry_point="generator",
    edges=[
        Edge(source="generator", target="self/need_reflections"),
        Edge(source="refiner", target="self/no_more_reflections"),
    ],
    mcpServers={
        "self": StdioServerParameters(command=sys.executable, args=[__file__, "mcp"])
    },
)

if __name__ == "__main__":
    import typer

    app = AsyncTyper()

    @app.command("agent")
    async def agent_main():
        reasoning = True
        async for chunk in await idea.run(
            messages=[{"role": "user", "content": "Let's start"}],
            context={"num_reflections": 3},
            stream=True,
        ):
            if hasattr(chunk.choices[0].delta, "reasoning"):
                typer.secho(
                    getattr(chunk.choices[0].delta, "reasoning"), nl=False, fg="green"
                )
                reasoning = True
            else:
                if reasoning:
                    typer.echo()
                reasoning = False
                typer.secho(chunk.choices[0].delta.content, nl=False)

    @app.command("mcp")
    def mcp_main():
        mcp.run()

    app()
