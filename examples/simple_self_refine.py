"""
Simple Self-Refine Agent Example

This example demonstrates a basic self-refinement pattern using SwarmX agents.
It shows how an agent can iteratively improve its responses through self-criticism
and refinement, with a maximum of 3 iterations to prevent infinite loops.

The pattern is:
1. Generate initial response
2. Self-critique the response  
3. Refine based on critique
4. Repeat up to 3 times or until satisfied
"""

import asyncio
from typing import Dict, Any

from swarmx import Agent


class SelfRefineContext:
    """Context to track refinement iterations."""
    
    def __init__(self, max_iterations: int = 3):
        self.iteration = 0
        self.max_iterations = max_iterations
        self.responses = []
        self.critiques = []
        
    def add_response(self, response: str):
        """Add a response to the history."""
        self.responses.append(response)
        
    def add_critique(self, critique: str):
        """Add a critique to the history."""
        self.critiques.append(critique)
        
    def should_continue(self) -> bool:
        """Check if we should continue refining."""
        return (self.iteration < self.max_iterations and 
                len(self.critiques) > 0 and 
                "APPROVED" not in self.critiques[-1])
        
    def next_iteration(self):
        """Move to next iteration."""
        self.iteration += 1


async def self_refine_response(task: str, max_iterations: int = 3) -> str:
    """
    Self-refine a response to a task through iterative improvement.
    
    Args:
        task: The task to complete
        max_iterations: Maximum number of refinement iterations
        
    Returns:
        The final refined response
    """
    
    # Create the generator agent
    generator = Agent(
        name="Generator",
        instructions=f"""You are a helpful assistant. Your task is: {task}

If this is your first response, provide a clear, helpful answer.

If you're refining a previous response, improve it based on the critique provided.
Focus on addressing the specific issues mentioned in the critique.

Always start your response with either "INITIAL RESPONSE:" or "REFINED RESPONSE:" followed by your answer.""",
        model="deepseek-reasoner"
    )
    
    # Create the critic agent
    critic = Agent(
        name="Critic",
        instructions="""You are a critic evaluating responses. Analyze the previous response for:

- Clarity and coherence
- Completeness and accuracy  
- Helpfulness to the user
- Areas for improvement

If the response is excellent and needs no improvement, respond with:
"APPROVED: This response is excellent and ready for the user."

Otherwise, provide constructive feedback in this format:
"CRITIQUE: [Specific issues that need improvement]
SUGGESTIONS: [Concrete suggestions for how to improve]"

Be specific and actionable in your feedback.""",
        model="claude-3.7-sonnet"
    )
    
    context = SelfRefineContext(max_iterations)
    
    # Initial generation
    print(f"🎯 Task: {task}")
    print("=" * 60)
    
    messages = [{"role": "user", "content": task}]
    
    # Generate initial response
    print(f"🤖 Iteration {context.iteration + 1}: Generating initial response...")
    response = await generator.run(messages=messages)
    
    if isinstance(response, list) and response:
        current_response = response[-1]["content"]
        context.add_response(current_response)
        print(f"📝 Response: {current_response[:200]}...")
        
        # Refinement loop
        while context.should_continue():
            context.next_iteration()
            
            # Get critique
            print(f"\n🔍 Iteration {context.iteration}: Getting critique...")
            critique_messages = [
                {"role": "user", "content": f"Please critique this response to the task '{task}':\n\n{current_response}"}
            ]
            
            critique_response = await critic.run(messages=critique_messages)
            if isinstance(critique_response, list) and critique_response:
                critique = critique_response[-1]["content"]
                context.add_critique(critique)
                print(f"💭 Critique: {critique[:200]}...")
                
                # Check if approved
                if "APPROVED" in critique:
                    print("✅ Response approved by critic!")
                    break
                    
                # Refine response
                print(f"\n🔄 Iteration {context.iteration}: Refining response...")
                refine_messages = [
                    {"role": "user", "content": f"Original task: {task}"},
                    {"role": "assistant", "content": current_response},
                    {"role": "user", "content": f"Critique: {critique}\n\nPlease improve your response based on this feedback."}
                ]
                
                refined_response = await generator.run(messages=refine_messages)
                if isinstance(refined_response, list) and refined_response:
                    current_response = refined_response[-1]["content"]
                    context.add_response(current_response)
                    print(f"📝 Refined Response: {current_response[:200]}...")
        
        print(f"\n🎉 Final response after {context.iteration} iterations:")
        print("=" * 60)
        return current_response
    
    return "No response generated"


async def run_examples():
    """Run several self-refinement examples."""
    
    examples = [
        "Write a concise explanation of how photosynthesis works",
        "Create a simple Python function to calculate the factorial of a number",
        "Explain the concept of machine learning to a 10-year-old",
    ]
    
    for i, task in enumerate(examples, 1):
        print(f"\n{'='*80}")
        print(f"EXAMPLE {i}: Self-Refine Agent System")
        print(f"{'='*80}")
        
        try:
            final_response = await self_refine_response(task, max_iterations=3)
            print(final_response)
            print(f"\n{'='*80}")
            
        except Exception as e:
            print(f"❌ Error in example {i}: {e}")
            
        # Add a small delay between examples
        await asyncio.sleep(1)


async def interactive_example():
    """Run an interactive self-refinement example."""
    print("\n" + "="*80)
    print("INTERACTIVE SELF-REFINE EXAMPLE")
    print("="*80)
    print("Enter a task and watch the agent refine its response!")
    print("(Press Ctrl+C to exit)")
    
    try:
        while True:
            task = input("\n📝 Enter your task: ").strip()
            if not task:
                continue
                
            print(f"\n🚀 Starting self-refinement for: {task}")
            final_response = await self_refine_response(task, max_iterations=3)
            print(f"\n✨ Final Result:\n{final_response}")
            
            continue_prompt = input("\n❓ Try another task? (y/n): ").strip().lower()
            if continue_prompt != 'y':
                break
                
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    print("🎯 Simple Self-Refine Agent System")
    print("This example demonstrates iterative response improvement")
    print("through self-criticism and refinement (max 3 iterations)")
    
    # Run predefined examples
    asyncio.run(run_examples())
    
    # Uncomment the line below to run interactive mode
    # asyncio.run(interactive_example())
