"""Simple Self-Refine Agent System Example.

This example demonstrates a self-refining agent system that can improve its own
responses through iterative refinement. The system consists of:

1. A Generator Agent that creates initial responses
2. A Critic Agent that evaluates and provides feedback
3. A Refiner Agent that improves responses based on feedback
4. A maximum of 3 refinement iterations to prevent infinite loops

The system uses SwarmX's graph-based agent orchestration to create a
self-improving workflow.
"""

import asyncio

from swarmx import Agent
from swarmx.agent import Edge


def create_self_refine_system(task_description: str) -> Agent:
    """Create a self-refining agent system.

    Args:
        task_description: The task that needs to be completed

    Returns:
        An Agent configured for self-refinement with sub-agents

    """
    # Generator Agent - Creates initial responses
    generator = Agent(
        name="Generator",
        instructions=f"""You are a Generator agent. Your task is to create an initial response to the following request:

{task_description}

Provide a clear, helpful response. Don't worry about perfection - you'll have opportunities to refine it.

Format your response as:
RESPONSE:
[Your response here]
""",
        model="deepseek-reasoner"
    )
    
    # Critic Agent - Evaluates responses and provides feedback
    critic = Agent(
        name="Critic",
        instructions="""You are a Critic agent. Your job is to evaluate the previous response and provide constructive feedback.

Analyze the response for:
- Clarity and coherence
- Completeness and accuracy
- Helpfulness to the user
- Areas for improvement

If the response is already excellent and needs no improvement, say "APPROVED: The response is excellent as-is."

Otherwise, provide specific, actionable feedback in this format:

FEEDBACK:
[Specific areas that need improvement]

SUGGESTIONS:
[Concrete suggestions for how to improve]
""",
        model="claude-3.7-sonnet"
    )
    
    # Refiner Agent - Improves responses based on feedback
    refiner = Agent(
        name="Refiner",
        instructions="""You are a Refiner agent. Your job is to improve the previous response based on the critic's feedback.

Review:
1. The original response
2. The critic's feedback and suggestions

Create an improved version that addresses the feedback while maintaining the core value of the original response.

If the critic approved the response, simply repeat it exactly.

Format your response as:
REFINED_RESPONSE:
[Your improved response here]
""",
        model="deepseek-reasoner"
    )
    
    # Decision Agent - Determines if more refinement is needed
    decision = Agent(
        name="Decision",
        instructions="""You are a Decision agent. Review the conversation history and determine if the response needs further refinement.

Consider:
- Has the response been approved by the critic?
- Have we reached the maximum number of refinements (3)?
- Is the response good enough for the user?

Respond with either:
- "CONTINUE" if more refinement is needed
- "DONE" if the response is ready

Current refinement count: {{ current_round | default(0) }}
Maximum refinements: 3
""",
        model="claude-3.7-sonnet"
    )
    
    # Create the main orchestrator agent with sub-agents
    orchestrator = Agent(
        name="SelfRefineOrchestrator",
        instructions="You are an orchestrator for a self-refining system.",
        entry_point="generator",
        nodes={
            "generator": generator,
            "critic": critic,
            "refiner": refiner,
            "decision": decision
        },
        edges=[
            Edge(source="generator", target="critic"),
            Edge(source="critic", target="refiner"),
            Edge(source="refiner", target="decision"),
        ]
    )

    return orchestrator


async def run_self_refine_example():
    """Run the self-refine agent system example."""
    
    # Define the task
    task = """
    Write a comprehensive guide on how to start learning Python programming 
    for complete beginners. Include practical steps, resources, and tips 
    for staying motivated.
    """
    
    print("🤖 Creating Self-Refine Agent System...")
    print(f"📝 Task: {task.strip()}")
    print("=" * 60)
    
    # Create the self-refining system
    refine_system = create_self_refine_system(task)

    # Initial message to start the process
    messages = [
        {
            "role": "user",
            "content": f"Please complete this task: {task}"
        }
    ]

    # Run the system
    print("🚀 Starting self-refinement process...")
    print("=" * 60)

    try:
        # Set context with refinement tracking
        context = {"current_round": 0, "max_rounds": 3}

        response = await refine_system.run(
            messages=messages,
            context=context
        )
        
        print("✅ Self-refinement process completed!")
        print("=" * 60)
        print("📄 Final Response:")
        if isinstance(response, list) and response:
            print(response[-1]["content"])
        else:
            print("No response received")
        
    except Exception as e:
        print(f"❌ Error during self-refinement: {e}")


async def run_simple_example():
    """Run a simpler example with just 2 agents."""
    
    print("\n" + "=" * 60)
    print("🔄 Running Simple Self-Refine Example")
    print("=" * 60)
    
    # Simple two-agent system: Writer -> Reviewer -> Writer
    writer = Agent(
        name="Writer",
        instructions="""You are a Writer. If this is your first response, write a short story about a robot learning to paint.

If you're receiving feedback, improve your story based on the suggestions while keeping the core narrative.

Always start your response with either "INITIAL STORY:" or "REVISED STORY:" followed by your story.""",
        model="deepseek-reasoner"
    )
    
    reviewer = Agent(
        name="Reviewer",
        instructions="""You are a Reviewer. Read the story and provide constructive feedback.

If the story is already excellent, respond with: "APPROVED: This story is excellent!"

Otherwise, provide specific feedback in this format:
FEEDBACK: [What needs improvement]
SUGGESTIONS: [How to improve it]

Focus on narrative flow, character development, and emotional impact.""",
        model="claude-3.7-sonnet"
    )
    
    # Create simple orchestrator
    simple_orchestrator = Agent(
        name="SimpleRefineOrchestrator",
        instructions="You orchestrate a simple writer-reviewer refinement process.",
        entry_point="writer",
        nodes={
            "writer": writer,
            "reviewer": reviewer
        },
        edges=[
            Edge(source="writer", target="reviewer"),
            # For simplicity, we'll do one round of refinement
        ]
    )
    
    # Run with refinement limit
    messages = [{"role": "user", "content": "Please write a short story about a robot learning to paint."}]
    
    try:
        response = await simple_orchestrator.run(
            messages=messages,
            context={"refinement_count": 0}
        )

        print("✅ Simple self-refinement completed!")
        print("📖 Final Story:")
        if isinstance(response, list) and response:
            print(response[-1]["content"])
        else:
            print("No response received")
        
    except Exception as e:
        print(f"❌ Error in simple example: {e}")


if __name__ == "__main__":
    print("🎯 Self-Refine Agent System Examples")
    print("=" * 60)
    
    # Run both examples
    asyncio.run(run_self_refine_example())
    asyncio.run(run_simple_example())
