[project]
name = "swarmx"
version = "0.7.0a0"
authors = [
    { name = "<PERSON> Ziya", email = "<EMAIL>" }
]
description = "A lightweight, stateless multi-agent orchestration framework."
readme = "README.md"
license = "MIT"
dependencies = [
    "anthropic>=0.64.0",
    "fastapi==0.116.1",
    "jinja2>=3.1.6",
    "mcp==1.12.1",
    "networkx==3.5",
    "openai==1.97.1",
    "python-dotenv>=1.1.1",
    "typer==0.16.0",
]
requires-python = ">=3.11"  # for asyncio.TaskGroup & except* syntax
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Distributed Computing",
    "Typing :: Typed",
]

[project.scripts]
swarmx = "swarmx:app"

[build-system]
requires = ["uv_build>=0.8.0,<0.9.0"]
build-backend = "uv_build"

[dependency-groups]
dev = [
    { include-group = "docs" },
    { include-group = "test" },
]
docs = [
    "mkdocs>=1.6.1",
    "mkdocs-gen-files>=0.5.0",
    "mkdocs-literate-nav>=0.6.2",
    "mkdocs-material>=9.6.17",
    "mkdocstrings[python]>=0.30.0",
]
test = [
    "mcp-server-time>=2025.8.4",
    "pytest>=8.4.1",
    "pytest-cov>=6.2.1",
]

[tool.ruff.lint]
extend-select = ["D", "F", "I", "T20"]
ignore = [
    "D203",  # incorrect-blank-line-before-class
    "D213",  # multi-line-summary-second-line
]

[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = [
    "D"
]
"examples/*.py" = ["T20", "D"]

[tool.mypy]
plugins = ['pydantic.mypy']

[tool.pytest.ini_options]
addopts = ["--cov=swarmx"]

[tool.pyright]
venvPath = "."
venv = ".venv"

[tool.coverage.report]
exclude_also = [
    "if __name__ == \"__main__\":",
    'case _( as .+)?:\n\s*assert_never\(.+\)',
]

[tool.tox]
requires = ["tox>=4", "tox-uv>=1"]
env_list = ["3.13", "3.12", "3.11"]

[tool.tox.env_run_base]
dependency_groups = ["test"]
commands = [["pytest", { replace = "posargs", default = ["tests"], extend = true }]]
