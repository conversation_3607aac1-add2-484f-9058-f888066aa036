"""MCP server implementation for SwarmX agents."""

import logging
from pathlib import Path
from typing import Any, Dict, List, TypedDict

from mcp.server import FastMCP
from openai.types.chat import ChatCompletionMessageParam

from .agent import Agent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MCPToolOutput(TypedDict):
    """TypedDict for MCP tool structured output."""

    messages: List[ChatCompletionMessageParam]
    context: Dict[str, Any] | None


def create_mcp_server(agent: Agent) -> FastMCP:
    """Create an MCP server that exposes a SwarmX agent as a tool.

    Args:
        agent: The SwarmX agent to expose as an MCP tool

    Returns:
        FastMCP server instance

    """
    # Create the MCP server
    mcp = FastMCP(f"swarmx-{agent.name}")

    @mcp.tool(structured_output=True)
    async def run_agent(
        messages: List[Dict[str, Any]], context: Dict[str, Any] | None = None
    ) -> MCPToolOutput:
        """Run the SwarmX agent with the provided messages.

        Args:
            messages: List of chat completion messages to send to the agent
            context: Optional context variables to pass to the agent

        Returns:
            CallToolResult with structured output containing messages and context

        """
        # Convert dict messages to proper ChatCompletionMessageParam format
        formatted_messages: List[ChatCompletionMessageParam] = []
        for msg in messages:
            formatted_messages.append(msg)  # type: ignore

        # Run the agent (non-streaming for MCP compatibility)
        result_messages = await agent.run(
            messages=formatted_messages, stream=False, context=context
        )

        # Create structured output for MCP tool hooks
        return {"messages": result_messages, "context": context}

    return mcp


def mcp(
    file: Path | None = None,
):
    """Start SwarmX as an MCP server."""
    # Load agent configuration
    if file is None:
        data = "{}"
    else:
        data = file.read_text()

    # Create agent
    agent = Agent.model_validate_json(data)  # type: ignore
    create_mcp_server(agent).run(transport="stdio")


if __name__ == "__main__":
    import typer
    app = typer.Typer()
    app.command()(mcp)
    app()
