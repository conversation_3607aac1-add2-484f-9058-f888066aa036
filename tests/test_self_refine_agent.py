"""Tests for the self-refine agent system example."""

import pytest
from unittest.mock import AsyncMock, MagicMock

from examples.self_refine_agent import create_self_refine_system
from swarmx import Agent


class TestSelfRefineAgent:
    """Test cases for the self-refine agent system."""
    
    def test_create_self_refine_system(self):
        """Test that the self-refine system is created correctly."""
        task = "Write a simple hello world program"
        orchestrator = create_self_refine_system(task)

        # Check that it returns an Agent
        assert isinstance(orchestrator, Agent)

        # Check that all required nodes are present
        assert "generator" in orchestrator.nodes
        assert "critic" in orchestrator.nodes
        assert "refiner" in orchestrator.nodes
        assert "decision" in orchestrator.nodes

        # Check agent names
        assert orchestrator.nodes["generator"].name == "Generator"
        assert orchestrator.nodes["critic"].name == "Critic"
        assert orchestrator.nodes["refiner"].name == "Refiner"
        assert orchestrator.nodes["decision"].name == "Decision"

        # Check that edges exist
        assert len(orchestrator.edges) >= 3  # At least the main flow edges
        
    def test_generator_agent_instructions(self):
        """Test that the generator agent has proper instructions."""
        task = "Test task description"
        orchestrator = create_self_refine_system(task)
        generator = orchestrator.nodes["generator"]

        assert task in generator.instructions
        assert "Generator agent" in generator.instructions
        assert "RESPONSE:" in generator.instructions
        
    def test_critic_agent_instructions(self):
        """Test that the critic agent has proper instructions."""
        orchestrator = create_self_refine_system("test task")
        critic = orchestrator.nodes["critic"]

        assert "Critic agent" in critic.instructions
        assert "APPROVED:" in critic.instructions
        assert "FEEDBACK:" in critic.instructions
        assert "SUGGESTIONS:" in critic.instructions

    def test_refiner_agent_instructions(self):
        """Test that the refiner agent has proper instructions."""
        orchestrator = create_self_refine_system("test task")
        refiner = orchestrator.nodes["refiner"]

        assert "Refiner agent" in refiner.instructions
        assert "REFINED_RESPONSE:" in refiner.instructions
        assert "feedback" in refiner.instructions.lower()

    def test_decision_agent_instructions(self):
        """Test that the decision agent has proper instructions."""
        orchestrator = create_self_refine_system("test task")
        decision = orchestrator.nodes["decision"]

        assert "Decision agent" in decision.instructions
        assert "CONTINUE" in decision.instructions
        assert "DONE" in decision.instructions
        assert "3" in decision.instructions  # Max refinements
        
    def test_agent_models(self):
        """Test that agents use appropriate models."""
        orchestrator = create_self_refine_system("test task")

        # Generator uses reasoning model
        assert orchestrator.nodes["generator"].model == "deepseek-reasoner"

        # Critic uses Claude for evaluation
        assert orchestrator.nodes["critic"].model == "claude-3.7-sonnet"

        # Refiner uses reasoning model
        assert orchestrator.nodes["refiner"].model == "deepseek-reasoner"

        # Decision uses Claude for decision making
        assert orchestrator.nodes["decision"].model == "claude-3.7-sonnet"
        
    def test_orchestrator_structure(self):
        """Test the orchestrator graph structure."""
        orchestrator = create_self_refine_system("test task")

        # Test that we have the right number of nodes
        assert len(orchestrator.nodes) == 4

        # Test that edges create the expected flow
        edge_pairs = [(edge.source, edge.target) for edge in orchestrator.edges if hasattr(edge, 'source')]

        # Should have main flow edges
        expected_edges = [
            ("generator", "critic"),
            ("critic", "refiner"),
            ("refiner", "decision")
        ]

        for expected_edge in expected_edges:
            # Check if this edge exists in some form
            found = any(
                (edge.source == expected_edge[0] and edge.target == expected_edge[1])
                for edge in orchestrator.edges
                if hasattr(edge, 'source') and hasattr(edge, 'target')
            )
            assert found, f"Expected edge {expected_edge} not found"
            
    def test_refinement_limit(self):
        """Test that the system has a refinement limit."""
        orchestrator = create_self_refine_system("test task")
        decision_agent = orchestrator.nodes["decision"]

        # Check that max refinements is mentioned in instructions
        assert "3" in decision_agent.instructions
        assert "Maximum refinements" in decision_agent.instructions

    def test_task_description_injection(self):
        """Test that task description is properly injected into generator."""
        custom_task = "This is a very specific custom task description"
        orchestrator = create_self_refine_system(custom_task)
        generator = orchestrator.nodes["generator"]

        assert custom_task in generator.instructions

    def test_different_models_used(self):
        """Test that different models are used for different roles."""
        orchestrator = create_self_refine_system("test task")

        models_used = {agent.model for agent in orchestrator.nodes.values()}

        # Should use at least 2 different models
        assert len(models_used) >= 2
        assert "deepseek-reasoner" in models_used
        assert "claude-3.7-sonnet" in models_used


class TestSelfRefineIntegration:
    """Integration tests for the self-refine system."""
    
    def test_system_creation_no_errors(self):
        """Test that creating the system doesn't raise errors."""
        try:
            orchestrator = create_self_refine_system("Write a hello world program")
            assert orchestrator is not None
            assert len(orchestrator.nodes) == 4
        except Exception as e:
            pytest.fail(f"System creation raised an exception: {e}")

    def test_edge_structure(self):
        """Test that edges are properly structured."""
        orchestrator = create_self_refine_system("test task")

        # Check that we have edges
        assert len(orchestrator.edges) >= 3

        # Check that edges have source and target attributes
        for edge in orchestrator.edges:
            assert hasattr(edge, 'source')
            assert hasattr(edge, 'target')
            assert edge.source in orchestrator.nodes
            assert edge.target in orchestrator.nodes


if __name__ == "__main__":
    pytest.main([__file__])
