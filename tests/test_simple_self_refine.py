"""Tests for the simple self-refine agent example."""

import pytest

from examples.simple_self_refine import SelfRefineContext


class TestSelfRefineContext:
    """Test cases for the SelfRefineContext class."""
    
    def test_init(self):
        """Test context initialization."""
        context = SelfRefineContext(max_iterations=5)
        assert context.iteration == 0
        assert context.max_iterations == 5
        assert context.responses == []
        assert context.critiques == []
        
    def test_add_response(self):
        """Test adding responses."""
        context = SelfRefineContext()
        context.add_response("First response")
        context.add_response("Second response")
        
        assert len(context.responses) == 2
        assert context.responses[0] == "First response"
        assert context.responses[1] == "Second response"
        
    def test_add_critique(self):
        """Test adding critiques."""
        context = SelfRefineContext()
        context.add_critique("First critique")
        context.add_critique("Second critique")
        
        assert len(context.critiques) == 2
        assert context.critiques[0] == "First critique"
        assert context.critiques[1] == "Second critique"
        
    def test_should_continue_no_critiques(self):
        """Test should_continue with no critiques."""
        context = SelfRefineContext(max_iterations=3)
        assert not context.should_continue()
        
    def test_should_continue_with_critiques(self):
        """Test should_continue with critiques."""
        context = SelfRefineContext(max_iterations=3)
        context.add_critique("Needs improvement")
        assert context.should_continue()
        
    def test_should_continue_approved(self):
        """Test should_continue when approved."""
        context = SelfRefineContext(max_iterations=3)
        context.add_critique("APPROVED: This is excellent")
        assert not context.should_continue()
        
    def test_should_continue_max_iterations(self):
        """Test should_continue at max iterations."""
        context = SelfRefineContext(max_iterations=2)
        context.iteration = 2
        context.add_critique("Needs improvement")
        assert not context.should_continue()
        
    def test_next_iteration(self):
        """Test incrementing iteration."""
        context = SelfRefineContext()
        assert context.iteration == 0
        context.next_iteration()
        assert context.iteration == 1
        context.next_iteration()
        assert context.iteration == 2


class TestSelfRefineResponse:
    """Test cases for the self_refine_response function."""
    
    def test_self_refine_response_structure(self):
        """Test that self_refine_response has the correct structure."""
        # This is a structural test since we can't easily test async without proper setup
        from examples.simple_self_refine import self_refine_response
        import inspect

        # Verify it's an async function
        assert inspect.iscoroutinefunction(self_refine_response)

        # Verify function signature
        sig = inspect.signature(self_refine_response)
        assert 'task' in sig.parameters
        assert 'max_iterations' in sig.parameters
        assert sig.parameters['max_iterations'].default == 3
    
    def test_context_creation(self):
        """Test that context is created correctly."""
        context = SelfRefineContext(max_iterations=5)
        
        # Test default values
        assert context.iteration == 0
        assert context.max_iterations == 5
        assert len(context.responses) == 0
        assert len(context.critiques) == 0
        
    def test_context_workflow(self):
        """Test the context workflow."""
        context = SelfRefineContext(max_iterations=3)
        
        # Initially should not continue (no critiques)
        assert not context.should_continue()
        
        # Add a critique that requires improvement
        context.add_critique("CRITIQUE: Needs more detail")
        assert context.should_continue()
        
        # Move to next iteration
        context.next_iteration()
        assert context.iteration == 1
        
        # Add an approval critique
        context.add_critique("APPROVED: This is excellent")
        assert not context.should_continue()
        
    def test_max_iterations_limit(self):
        """Test that max iterations are respected."""
        context = SelfRefineContext(max_iterations=2)
        
        # Add critique and iterate to max
        context.add_critique("Needs improvement")
        context.next_iteration()  # iteration = 1
        context.next_iteration()  # iteration = 2
        
        # Should not continue even with critique
        context.add_critique("Still needs improvement")
        assert not context.should_continue()
        
    def test_approved_response_stops_iteration(self):
        """Test that approved responses stop iteration."""
        context = SelfRefineContext(max_iterations=5)
        
        # Add approved critique
        context.add_critique("APPROVED: Perfect response")
        
        # Should not continue even though we're under max iterations
        assert not context.should_continue()


class TestIntegration:
    """Integration tests for the self-refine system."""
    
    def test_context_integration(self):
        """Test context integration with the refinement process."""
        context = SelfRefineContext(max_iterations=3)
        
        # Simulate the refinement process
        context.add_response("Initial response")
        context.add_critique("Needs improvement")
        
        # Should continue
        assert context.should_continue()
        
        # Next iteration
        context.next_iteration()
        context.add_response("Improved response")
        context.add_critique("APPROVED: Excellent")
        
        # Should stop
        assert not context.should_continue()
        
        # Verify final state
        assert context.iteration == 1
        assert len(context.responses) == 2
        assert len(context.critiques) == 2
        
    def test_edge_cases(self):
        """Test edge cases."""
        # Zero max iterations
        context = SelfRefineContext(max_iterations=0)
        context.add_critique("Needs work")
        assert not context.should_continue()
        
        # Empty critique
        context = SelfRefineContext(max_iterations=3)
        context.add_critique("")
        assert context.should_continue()  # Empty string doesn't contain "APPROVED"


if __name__ == "__main__":
    pytest.main([__file__])
