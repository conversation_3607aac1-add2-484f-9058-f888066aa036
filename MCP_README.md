# SwarmX MCP Server Support

SwarmX now supports running as an MCP (Model Context Protocol) server, allowing SwarmX agents to be used as tools in MCP-compatible clients like <PERSON>.

## Features

- **Single Agent MCP Server**: Expose a single SwarmX agent as an MCP tool
- **Multi-Agent MCP Server**: Expose multiple SwarmX agents as separate MCP tools
- **Structured Output**: Proper MCP tool hook support with structured output containing messages and context
- **CLI Integration**: Easy command-line interface to start MCP servers
- **Programmatic API**: Create MCP servers programmatically in your code

## Quick Start

### 1. Create an Agent Configuration

Create a JSON file with your agent configuration:

```json
{
  "name": "My<PERSON><PERSON>",
  "instructions": "You are a helpful assistant.",
  "model": "gpt-3.5-turbo"
}
```

### 2. Start the MCP Server

```bash
# Start MCP server with stdio transport (default)
swarmx mcp --file agent.json

# Specify transport explicitly
swarmx mcp --file agent.json --transport stdio
```

### 3. Use with <PERSON>

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "swarmx": {
      "command": "python",
      "args": ["-m", "swarmx", "mcp", "--file", "/absolute/path/to/agent.json"]
    }
  }
}
```

## Programmatic Usage

### Single Agent Server

```python
from swarmx import Agent, create_mcp_server

# Create an agent
agent = Agent(
    name="HelperAgent",
    instructions="You are a helpful assistant.",
    model="gpt-3.5-turbo"
)

# Create and run MCP server
mcp_server = create_mcp_server(agent)
mcp_server.run(transport="stdio")
```

## MCP Tool Output

SwarmX MCP tools return structured output compatible with MCP tool hooks:

```python
{
    "messages": [
        # List of ChatCompletionMessageParam objects
        # containing the agent's response messages
    ],
    "context": {
        # Optional context dictionary passed to the agent
    }
}
```

## CLI Commands

### `swarmx mcp`

Start SwarmX as an MCP server.

**Options:**
- `--file, -f`: Path to the SwarmX agent configuration file (JSON)
- `--transport`: Transport type for MCP server (`stdio` or `sse`, default: `stdio`)

**Examples:**
```bash
# Basic usage
swarmx mcp --file my_agent.json

# With explicit transport
swarmx mcp --file my_agent.json --transport stdio
```

## Integration Examples

### Claude Desktop Configuration

```json
{
  "mcpServers": {
    "swarmx-assistant": {
      "command": "swarmx",
      "args": ["mcp", "--file", "/path/to/assistant.json"]
    },
    "swarmx-coder": {
      "command": "python",
      "args": ["-m", "swarmx", "mcp", "--file", "/path/to/coder.json"]
    }
  }
}
```

### Custom MCP Client

```python
import asyncio
from mcp.client.stdio import stdio_client
from mcp import ClientSession

async def use_swarmx_mcp():
    # Connect to SwarmX MCP server
    read_stream, write_stream = await stdio_client(
        command="python",
        args=["-m", "swarmx", "mcp", "--file", "agent.json"]
    )
    
    async with ClientSession(read_stream, write_stream) as session:
        await session.initialize()
        
        # List available tools
        tools = await session.list_tools()
        print(f"Available tools: {[tool.name for tool in tools.tools]}")
        
        # Call the agent tool
        result = await session.call_tool(
            "run_agent",
            {
                "messages": [{"role": "user", "content": "Hello!"}],
                "context": {"user_id": "123"}
            }
        )
        
        print(f"Result: {result}")

asyncio.run(use_swarmx_mcp())
```

## Error Handling

The MCP server includes proper error handling:

- Invalid agent configurations are caught during startup
- Runtime errors during agent execution are returned as MCP error responses
- Logging is configured to help with debugging

## Limitations

- Currently only supports `stdio` transport (SSE support planned)
- Streaming responses are converted to non-streaming for MCP compatibility
- Agent context is passed through but not modified by the MCP layer

## Troubleshooting

### Server Won't Start
- Check that the agent configuration file exists and is valid JSON
- Verify that all required dependencies are installed
- Check the console output for specific error messages

### Tools Not Appearing in Client
- Ensure the MCP client configuration is correct
- Verify the file paths are absolute paths
- Restart the MCP client after configuration changes

### Agent Execution Errors
- Check that the specified model is available
- Verify API keys are properly configured
- Review the agent instructions for any issues
